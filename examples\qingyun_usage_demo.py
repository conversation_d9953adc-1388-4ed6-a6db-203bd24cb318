#!/usr/bin/env python3
"""
QingYun API 使用演示
展示如何在代码中使用QingYun提供的各种AI模型
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import Literal, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider
from src.utils.llm import call_llm
from langchain_core.prompts import ChatPromptTemplate

class InvestmentSignal(BaseModel):
    """投资信号模型"""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100, description="信心水平 0-100")
    reasoning: str = Field(description="分析推理")
    price_target: Optional[float] = Field(None, description="目标价格")
    risk_level: Literal["low", "medium", "high"] = Field(description="风险等级")

def demo_basic_chat():
    """演示基本对话功能"""
    print("🗣️ 基本对话演示")
    print("-" * 40)
    
    # 加载环境变量
    load_dotenv()
    
    # 获取模型
    model = get_model("gpt-4o", ModelProvider.QINGYUN)
    
    # 简单对话
    response = model.invoke("请简单介绍一下量化投资的基本概念。")
    
    print(f"用户: 请简单介绍一下量化投资的基本概念。")
    print(f"AI: {response.content}")
    print()

def demo_structured_output():
    """演示结构化输出功能"""
    print("🏗️ 结构化输出演示")
    print("-" * 40)
    
    # 创建金融分析提示
    prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一位专业的金融分析师。请根据提供的信息进行股票分析，
        并严格按照指定的JSON格式返回结果。"""),
        ("human", """请分析特斯拉(TSLA)的投资前景：

当前股价: $248.50
市盈率: 65.2
近期新闻: 特斯拉Q4交付量超预期，新工厂产能提升
技术指标: RSI 58, MACD呈上升趋势
行业趋势: 电动车市场持续增长

请给出详细的投资建议。""")
    ])
    
    # 使用call_llm进行结构化输出
    result = call_llm(
        prompt=prompt,
        model_name="claude-3-5-sonnet-latest",
        model_provider="QingYun",
        pydantic_model=InvestmentSignal,
        agent_name="demo_analyst"
    )
    
    print(f"分析结果:")
    print(f"  信号: {result.signal}")
    print(f"  信心水平: {result.confidence}%")
    print(f"  风险等级: {result.risk_level}")
    print(f"  目标价格: ${result.price_target}" if result.price_target else "  目标价格: 未设定")
    print(f"  分析推理: {result.reasoning}")
    print()

def demo_model_comparison():
    """演示不同模型的对比"""
    print("⚖️ 模型对比演示")
    print("-" * 40)
    
    # 要对比的模型
    models_to_compare = [
        ("gpt-4o", "OpenAI GPT-4o"),
        ("claude-3-5-sonnet-latest", "Anthropic Claude"),
        ("meta-llama/llama-4-scout", "Meta Llama"),
    ]
    
    question = "什么是价值投资？请用一句话概括。"
    print(f"问题: {question}\n")
    
    for model_name, display_name in models_to_compare:
        try:
            model = get_model(model_name, ModelProvider.QINGYUN)
            response = model.invoke(question)
            
            print(f"🤖 {display_name}:")
            print(f"   {response.content}")
            print()
            
        except Exception as e:
            print(f"❌ {display_name}: 调用失败 - {e}")
            print()

def demo_financial_analysis_pipeline():
    """演示完整的金融分析流程"""
    print("📊 金融分析流程演示")
    print("-" * 40)
    
    # 模拟股票数据
    stock_data = {
        "ticker": "AAPL",
        "current_price": 225.50,
        "pe_ratio": 28.5,
        "market_cap": "3.5T",
        "recent_news": [
            "苹果发布新的AI功能，iPhone销量超预期",
            "苹果服务业务收入创历史新高",
            "分析师上调苹果目标价至250美元"
        ],
        "technical_indicators": {
            "rsi": 65,
            "macd": "上升趋势",
            "moving_average": "价格位于20日均线上方"
        }
    }
    
    # 创建分析提示
    prompt = ChatPromptTemplate.from_messages([
        ("system", """你是一位资深的股票分析师。请基于提供的数据进行全面分析，
        包括基本面、技术面和市场情绪，并给出明确的投资建议。"""),
        ("human", """请分析以下股票数据：

股票代码: {ticker}
当前价格: ${current_price}
市盈率: {pe_ratio}
市值: {market_cap}

近期新闻:
{news}

技术指标:
- RSI: {rsi}
- MACD: {macd}
- 均线: {ma}

请提供详细的投资分析和建议。""".format(
            ticker=stock_data["ticker"],
            current_price=stock_data["current_price"],
            pe_ratio=stock_data["pe_ratio"],
            market_cap=stock_data["market_cap"],
            news="\n".join([f"- {news}" for news in stock_data["recent_news"]]),
            rsi=stock_data["technical_indicators"]["rsi"],
            macd=stock_data["technical_indicators"]["macd"],
            ma=stock_data["technical_indicators"]["moving_average"]
        ))
    ])
    
    # 使用高质量模型进行分析
    result = call_llm(
        prompt=prompt,
        model_name="claude-3-5-sonnet-latest",
        model_provider="QingYun",
        pydantic_model=InvestmentSignal,
        agent_name="financial_analyst"
    )
    
    print(f"📈 {stock_data['ticker']} 投资分析报告")
    print(f"{'='*50}")
    print(f"投资信号: {result.signal.upper()}")
    print(f"信心水平: {result.confidence}%")
    print(f"风险评级: {result.risk_level.upper()}")
    if result.price_target:
        print(f"目标价格: ${result.price_target}")
    print(f"\n分析详情:")
    print(f"{result.reasoning}")
    print()

def demo_error_handling():
    """演示错误处理"""
    print("🛡️ 错误处理演示")
    print("-" * 40)
    
    try:
        # 尝试使用不存在的模型
        model = get_model("non-existent-model", ModelProvider.QINGYUN)
        response = model.invoke("测试")
        
    except Exception as e:
        print(f"✅ 成功捕获错误: {type(e).__name__}")
        print(f"   错误信息: {e}")
        print(f"   建议: 检查模型名称是否正确")
        print()

def main():
    """主演示函数"""
    print("🚀 QingYun API 使用演示")
    print("=" * 50)
    
    # 检查API密钥
    load_dotenv()
    api_key = os.getenv("QINGYUN_API_KEY")
    if not api_key:
        print("❌ 请先在.env文件中设置QINGYUN_API_KEY")
        return
    
    print(f"✅ API密钥已配置: {api_key[:10]}...")
    print()
    
    try:
        # 运行各种演示
        demo_basic_chat()
        demo_structured_output()
        demo_model_comparison()
        demo_financial_analysis_pipeline()
        demo_error_handling()
        
        print("🎉 演示完成！")
        print("\n📋 下一步:")
        print("1. 运行测试脚本验证所有模型: python test_api/test_qingyun_quick.py")
        print("2. 在回测中使用QingYun模型: python src/backtester.py")
        print("3. 查看详细文档: docs/qingyun_testing_guide.md")
        
    except KeyboardInterrupt:
        print("\n⚠️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查网络连接和API密钥配置")

if __name__ == "__main__":
    main()
