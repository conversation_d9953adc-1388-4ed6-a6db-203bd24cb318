@echo off
chcp 65001 >nul
echo.
echo 🚀 QingYun API 模型测试工具
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python
    pause
    exit /b 1
)

REM 检查.env文件是否存在
if not exist ".env" (
    echo ❌ 错误: 未找到.env文件，请先配置环境变量
    echo 请创建.env文件并添加: QINGYUN_API_KEY=your_api_key
    pause
    exit /b 1
)

REM 显示菜单
:menu
echo.
echo 请选择测试选项:
echo.
echo 1. 快速连接测试
echo 2. 快速模型测试 (推荐)
echo 3. 综合性能测试
echo 4. 测试指定模型
echo 5. 查看可用模型列表
echo 6. 运行使用演示
echo 7. 退出
echo.
set /p choice=请输入选项 (1-7): 

if "%choice%"=="1" goto connection_test
if "%choice%"=="2" goto quick_test
if "%choice%"=="3" goto comprehensive_test
if "%choice%"=="4" goto single_model_test
if "%choice%"=="5" goto list_models
if "%choice%"=="6" goto usage_demo
if "%choice%"=="7" goto exit
echo 无效选项，请重新选择
goto menu

:connection_test
echo.
echo 🔗 正在测试API连接...
python test_api/test_qingyun_quick.py --connection
echo.
pause
goto menu

:quick_test
echo.
echo ⚡ 正在运行快速测试...
python test_api/test_qingyun_quick.py
echo.
pause
goto menu

:comprehensive_test
echo.
echo 📊 正在运行综合测试 (可能需要几分钟)...
python test_api/test_qingyun_comprehensive.py
echo.
pause
goto menu

:single_model_test
echo.
set /p model_name=请输入模型名称 (如: gpt-4o): 
if "%model_name%"=="" (
    echo 模型名称不能为空
    goto menu
)
echo.
echo 🧪 正在测试模型: %model_name%
python test_api/test_qingyun_quick.py --model "%model_name%"
echo.
pause
goto menu

:list_models
echo.
echo 📋 可用模型列表:
python test_api/test_qingyun_comprehensive.py --list
echo.
pause
goto menu

:usage_demo
echo.
echo 🎯 正在运行使用演示...
python examples/qingyun_usage_demo.py
echo.
pause
goto menu

:exit
echo.
echo 👋 感谢使用 QingYun API 测试工具！
echo.
pause
exit /b 0
