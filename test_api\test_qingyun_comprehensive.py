#!/usr/bin/env python3
"""
QingYun API 模型综合测试脚本
测试所有通过qinyun API提供的模型，包括基本调用、结构化输出、金融分析等功能
"""

import os
import sys
import json
import time
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field
from typing import Literal, List, Dict, Any, Optional
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider, get_model_info
from src.utils.llm import call_llm
from langchain_core.prompts import ChatPromptTemplate

class TestSignal(BaseModel):
    """测试用的交易信号模型"""
    signal: Literal["bullish", "bearish", "neutral"]
    confidence: float = Field(ge=0, le=100, description="信心水平 0-100")
    reasoning: str = Field(description="信号推理")
    price_target: Optional[float] = Field(None, description="目标价格")

class ModelPerformance(BaseModel):
    """模型性能测试结果"""
    model_name: str
    response_time: float
    success: bool
    error_message: Optional[str] = None
    response_quality: int = Field(ge=1, le=5, description="响应质量评分 1-5")

class QingYunModelTester:
    """QingYun模型综合测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 加载环境变量
        load_dotenv()
        
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("QINGYUN_API_KEY环境变量未设置。请在.env文件中设置您的QingYun API密钥。")
        
        # 要测试的模型列表
        self.test_models = [
            "claude-3-5-haiku-latest",
            "claude-3-5-sonnet-latest", 
            "claude-3-7-sonnet-latest",
            "gemini-2.0-flash",
            "gemini-2.5-pro-exp-03-25",
            "gpt-4o",
            "gpt-4.5-preview",
            "o3",
            "o4-mini",
            "meta-llama/llama-4-scout",
            "meta-llama/llama-4-maverick",
            "grok-3-mini-beta"
        ]
        
        self.results = []
        
        print(f"🚀 QingYun API 模型综合测试器")
        print(f"✅ API密钥已配置: {self.api_key[:10]}...")
        print(f"📋 将测试 {len(self.test_models)} 个模型")
        print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("-" * 60)

    def create_test_prompts(self) -> Dict[str, ChatPromptTemplate]:
        """创建不同类型的测试提示词"""
        prompts = {}
        
        # 1. 基本对话测试
        prompts["basic"] = ChatPromptTemplate.from_messages([
            ("system", "你是一个有用的AI助手。"),
            ("human", "请简单介绍一下人工智能的发展历程，控制在100字以内。")
        ])
        
        # 2. 金融分析测试
        prompts["financial"] = ChatPromptTemplate.from_messages([
            ("system", """你是一位专业的金融分析师。请根据提供的信息进行股票分析。
            
请严格按照以下JSON格式返回结果：
{{
  "signal": "bullish" 或 "bearish" 或 "neutral",
  "confidence": 数字(0-100),
  "reasoning": "详细分析原因",
  "price_target": 数字或null
}}"""),
            ("human", """请分析苹果公司(AAPL)的投资前景：

当前股价: $225.50
市盈率: 28.5
近期新闻: 苹果发布了新的AI功能，iPhone销量超预期
技术指标: RSI 65, MACD呈上升趋势

请给出投资建议。""")
        ])
        
        # 3. 创意写作测试
        prompts["creative"] = ChatPromptTemplate.from_messages([
            ("system", "你是一位创意写作专家。"),
            ("human", "请写一个关于AI交易员的50字微小说。")
        ])
        
        # 4. 逻辑推理测试
        prompts["logic"] = ChatPromptTemplate.from_messages([
            ("system", "你是一位逻辑推理专家。"),
            ("human", """有三个盒子，每个盒子里都有两个球。
盒子A：两个红球
盒子B：两个蓝球  
盒子C：一个红球一个蓝球

现在随机选择一个盒子，从中取出一个球，发现是红球。
请问这个红球来自盒子A的概率是多少？请详细解释推理过程。""")
        ])
        
        return prompts

    def test_model_basic(self, model_name: str) -> Dict[str, Any]:
        """测试模型基本功能"""
        print(f"  📝 基本对话测试...")
        
        try:
            start_time = time.time()
            
            # 获取模型
            llm = get_model(model_name, ModelProvider.QINGYUN)
            
            # 创建提示
            prompt = self.create_test_prompts()["basic"]
            
            # 调用模型
            response = llm.invoke(prompt)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 评估响应质量
            response_text = response.content if hasattr(response, 'content') else str(response)
            quality_score = self.evaluate_response_quality(response_text, "basic")
            
            return {
                "success": True,
                "response_time": response_time,
                "response": response_text[:200] + "..." if len(response_text) > 200 else response_text,
                "quality_score": quality_score,
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "response_time": 0,
                "response": None,
                "quality_score": 0,
                "error": str(e)
            }

    def test_model_structured(self, model_name: str) -> Dict[str, Any]:
        """测试模型结构化输出功能"""
        print(f"  🏗️ 结构化输出测试...")
        
        try:
            start_time = time.time()
            
            # 使用call_llm函数进行结构化输出测试
            prompt = self.create_test_prompts()["financial"]
            
            result = call_llm(
                prompt=prompt,
                model_name=model_name,
                model_provider="QingYun",
                pydantic_model=TestSignal,
                agent_name="test_agent",
                max_retries=2
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 验证结构化输出
            is_valid = isinstance(result, TestSignal) and result.signal in ["bullish", "bearish", "neutral"]
            quality_score = 5 if is_valid else 1
            
            return {
                "success": True,
                "response_time": response_time,
                "response": result.model_dump() if is_valid else str(result),
                "quality_score": quality_score,
                "error": None
            }
            
        except Exception as e:
            return {
                "success": False,
                "response_time": 0,
                "response": None,
                "quality_score": 0,
                "error": str(e)
            }

    def test_model_comprehensive(self, model_name: str) -> Dict[str, Any]:
        """对单个模型进行综合测试"""
        print(f"\n🧪 测试模型: {model_name}")
        print(f"  ⏱️ 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 基本功能测试
        basic_result = self.test_model_basic(model_name)
        
        # 结构化输出测试
        structured_result = self.test_model_structured(model_name)
        
        # 综合评分
        overall_score = (basic_result["quality_score"] + structured_result["quality_score"]) / 2
        total_time = basic_result["response_time"] + structured_result["response_time"]
        
        success = basic_result["success"] and structured_result["success"]
        
        result = {
            "model_name": model_name,
            "success": success,
            "total_time": total_time,
            "overall_score": overall_score,
            "basic_test": basic_result,
            "structured_test": structured_result,
            "timestamp": datetime.now().isoformat()
        }
        
        # 显示结果
        if success:
            print(f"  ✅ 测试成功 | 总耗时: {total_time:.2f}s | 综合评分: {overall_score:.1f}/5")
        else:
            errors = []
            if not basic_result["success"]:
                errors.append(f"基本测试: {basic_result['error']}")
            if not structured_result["success"]:
                errors.append(f"结构化测试: {structured_result['error']}")
            print(f"  ❌ 测试失败 | 错误: {'; '.join(errors)}")
        
        return result

    def evaluate_response_quality(self, response: str, test_type: str) -> int:
        """评估响应质量 (1-5分)"""
        if not response or len(response.strip()) < 10:
            return 1
        
        score = 3  # 基础分
        
        if test_type == "basic":
            # 检查是否包含关键词
            keywords = ["人工智能", "AI", "发展", "历程"]
            if any(keyword in response for keyword in keywords):
                score += 1
            
            # 检查长度是否合适
            if 50 <= len(response) <= 150:
                score += 1
        
        return min(score, 5)

    def run_all_tests(self) -> List[Dict[str, Any]]:
        """运行所有模型的测试"""
        print(f"\n🚀 开始批量测试...")

        for i, model_name in enumerate(self.test_models, 1):
            print(f"\n[{i}/{len(self.test_models)}]", end=" ")

            try:
                result = self.test_model_comprehensive(model_name)
                self.results.append(result)

                # 避免API限制，测试间隔
                if i < len(self.test_models):
                    time.sleep(2)

            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断测试")
                break
            except Exception as e:
                print(f"❌ 模型 {model_name} 测试异常: {e}")
                self.results.append({
                    "model_name": model_name,
                    "success": False,
                    "total_time": 0,
                    "overall_score": 0,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                })

        return self.results

    def generate_report(self) -> str:
        """生成测试报告"""
        if not self.results:
            return "❌ 没有测试结果"

        # 统计数据
        total_models = len(self.results)
        successful_models = len([r for r in self.results if r["success"]])
        failed_models = total_models - successful_models

        # 成功的模型按性能排序
        successful_results = [r for r in self.results if r["success"]]
        successful_results.sort(key=lambda x: (-x["overall_score"], x["total_time"]))

        # 生成报告
        report = []
        report.append("=" * 60)
        report.append("🎯 QingYun API 模型测试报告")
        report.append("=" * 60)
        report.append(f"📊 测试概览:")
        report.append(f"   • 总测试模型: {total_models}")
        report.append(f"   • 成功模型: {successful_models}")
        report.append(f"   • 失败模型: {failed_models}")
        report.append(f"   • 成功率: {successful_models/total_models*100:.1f}%")

        if successful_results:
            report.append(f"\n🏆 性能排行榜 (按综合评分和响应时间排序):")
            for i, result in enumerate(successful_results[:10], 1):
                report.append(f"   {i:2d}. {result['model_name']:<35} | "
                            f"评分: {result['overall_score']:.1f}/5 | "
                            f"耗时: {result['total_time']:.2f}s")

        # 失败的模型
        failed_results = [r for r in self.results if not r["success"]]
        if failed_results:
            report.append(f"\n❌ 失败模型:")
            for result in failed_results:
                error_msg = result.get("error", "未知错误")
                report.append(f"   • {result['model_name']}: {error_msg}")

        # 推荐使用
        if successful_results:
            best_model = successful_results[0]
            report.append(f"\n💡 推荐使用:")
            report.append(f"   🥇 最佳综合性能: {best_model['model_name']}")
            report.append(f"      评分: {best_model['overall_score']:.1f}/5, 耗时: {best_model['total_time']:.2f}s")

            # 最快模型
            fastest_model = min(successful_results, key=lambda x: x["total_time"])
            if fastest_model != best_model:
                report.append(f"   ⚡ 最快响应: {fastest_model['model_name']}")
                report.append(f"      耗时: {fastest_model['total_time']:.2f}s, 评分: {fastest_model['overall_score']:.1f}/5")

        report.append(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("=" * 60)

        return "\n".join(report)

    def save_detailed_results(self, filename: Optional[str] = None):
        """保存详细测试结果到JSON文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"qingyun_test_results_{timestamp}.json"

        filepath = Path("test_api") / filename

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, ensure_ascii=False, indent=2)

        print(f"📄 详细结果已保存到: {filepath}")
        return filepath

def test_single_model(model_name: str):
    """测试单个模型的快速函数"""
    print(f"🧪 快速测试单个模型: {model_name}")

    try:
        tester = QingYunModelTester()
        tester.test_models = [model_name]  # 只测试指定模型

        result = tester.test_model_comprehensive(model_name)

        if result["success"]:
            print(f"\n✅ 测试成功!")
            print(f"   📊 综合评分: {result['overall_score']:.1f}/5")
            print(f"   ⏱️ 总耗时: {result['total_time']:.2f}s")
            print(f"   📝 基本测试: {result['basic_test']['response']}")
            print(f"   🏗️ 结构化测试: {result['structured_test']['response']}")
        else:
            print(f"\n❌ 测试失败!")
            if result.get("basic_test", {}).get("error"):
                print(f"   基本测试错误: {result['basic_test']['error']}")
            if result.get("structured_test", {}).get("error"):
                print(f"   结构化测试错误: {result['structured_test']['error']}")

        return result

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="QingYun API 模型测试工具")
    parser.add_argument("--model", "-m", type=str, help="测试单个模型")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有可用模型")
    parser.add_argument("--quick", "-q", action="store_true", help="快速测试(跳过部分模型)")

    args = parser.parse_args()

    try:
        if args.list:
            # 列出所有可用模型
            tester = QingYunModelTester()
            print(f"\n📋 可用的QingYun模型:")
            for i, model in enumerate(tester.test_models, 1):
                print(f"   {i:2d}. {model}")
            print(f"\n使用方法:")
            print(f"   python test_api/test_qingyun_comprehensive.py -m model_name")
            return

        if args.model:
            # 测试单个模型
            test_single_model(args.model)
            return

        # 创建测试器
        tester = QingYunModelTester()

        if args.quick:
            # 快速测试，只测试部分模型
            tester.test_models = tester.test_models[:6]  # 只测试前6个模型
            print(f"🚀 快速测试模式，只测试前6个模型")

        # 运行测试
        results = tester.run_all_tests()

        # 生成并显示报告
        report = tester.generate_report()
        print(f"\n{report}")

        # 保存详细结果
        tester.save_detailed_results()

        # 使用建议
        print(f"\n📋 使用建议:")
        print(f"1. 在回测系统中使用:")
        print(f"   python src/backtester.py")
        print(f"   选择 [qingyun] 开头的模型")
        print(f"")
        print(f"2. 在主程序中使用:")
        print(f"   python src/main.py")
        print(f"   选择 [qingyun] 开头的模型")
        print(f"")
        print(f"3. 程序化调用:")
        print(f"   from src.llm.models import get_model, ModelProvider")
        print(f"   llm = get_model('model_name', ModelProvider.QINGYUN)")

    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
