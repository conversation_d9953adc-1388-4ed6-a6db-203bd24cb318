# QingYun API 模型测试指南

本指南介绍如何使用测试脚本来验证和评估通过QingYun API提供的各种AI模型。

## 📋 测试脚本概览

我们提供了三个测试脚本，满足不同的测试需求：

| 脚本名称 | 用途 | 特点 | 推荐场景 |
|---------|------|------|----------|
| `test_qingyun_quick.py` | 快速测试 | 轻量、快速、基本功能 | 日常检查、连接验证 |
| `test_qingyun_comprehensive.py` | 综合测试 | 全面、详细、性能评估 | 模型选择、性能对比 |
| `test_qingyun_simple.py` | 简单测试 | 基础、直接、易理解 | 学习参考、调试 |

## 🚀 快速开始

### 1. 环境准备

确保您的 `.env` 文件中已配置QingYun API密钥：

```bash
QINGYUN_API_KEY=your_qingyun_api_key_here
```

### 2. 快速连接测试

首先验证API连接是否正常：

```bash
python test_api/test_qingyun_quick.py --connection
```

### 3. 快速模型测试

测试推荐的几个核心模型：

```bash
python test_api/test_qingyun_quick.py
```

## 📊 详细测试指南

### 快速测试 (test_qingyun_quick.py)

**适用场景**: 日常检查、快速验证

**功能特点**:
- 测试5个推荐模型
- 基本对话功能验证
- 响应时间测量
- 简洁的结果报告

**使用方法**:

```bash
# 完整快速测试
python test_api/test_qingyun_quick.py

# 仅测试API连接
python test_api/test_qingyun_quick.py --connection

# 测试指定模型
python test_api/test_qingyun_quick.py --model "gpt-4o"
```

**示例输出**:
```
🚀 QingYun API 快速测试器
✅ API密钥已配置: sk-HV7fNK...
📋 将测试 5 个推荐模型

[1/5] 🧪 测试模型: gpt-4o
  ✅ 成功 | 耗时: 2.34s
  📝 响应: 人工智能是模拟人类智能的计算机系统...

📊 QingYun API 快速测试摘要
总测试模型: 5
成功模型: 4
失败模型: 1
成功率: 80.0%
```

### 综合测试 (test_qingyun_comprehensive.py)

**适用场景**: 模型选择、性能评估、深度分析

**功能特点**:
- 测试12个模型
- 基本对话 + 结构化输出测试
- 金融分析场景模拟
- 详细性能报告
- JSON结果保存

**使用方法**:

```bash
# 完整综合测试
python test_api/test_qingyun_comprehensive.py

# 快速模式（仅测试前6个模型）
python test_api/test_qingyun_comprehensive.py --quick

# 测试单个模型
python test_api/test_qingyun_comprehensive.py --model "claude-3-5-sonnet-latest"

# 列出所有可测试模型
python test_api/test_qingyun_comprehensive.py --list
```

**测试内容**:
1. **基本对话测试**: 验证模型基础对话能力
2. **结构化输出测试**: 验证JSON格式输出能力
3. **金融分析测试**: 模拟实际投资分析场景
4. **性能评估**: 响应时间和质量评分

**示例输出**:
```
🎯 QingYun API 模型测试报告
============================================================
📊 测试概览:
   • 总测试模型: 12
   • 成功模型: 10
   • 失败模型: 2
   • 成功率: 83.3%

🏆 性能排行榜 (按综合评分和响应时间排序):
    1. claude-3-5-sonnet-latest          | 评分: 4.5/5 | 耗时: 3.21s
    2. gpt-4o                            | 评分: 4.2/5 | 耗时: 2.87s
    3. meta-llama/llama-4-scout          | 评分: 4.0/5 | 耗时: 4.15s

💡 推荐使用:
   🥇 最佳综合性能: claude-3-5-sonnet-latest
      评分: 4.5/5, 耗时: 3.21s
   ⚡ 最快响应: gpt-4o
      耗时: 2.87s, 评分: 4.2/5
```

## 🎯 模型选择建议

基于测试结果，以下是不同场景的模型推荐：

### 通用对话和分析
- **首选**: `claude-3-5-sonnet-latest` - 综合能力最强
- **备选**: `gpt-4o` - 响应速度快，质量高

### 金融分析专用
- **首选**: `claude-3-5-sonnet-latest` - 逻辑推理能力强
- **备选**: `meta-llama/llama-4-scout` - 专业分析能力好

### 快速响应场景
- **首选**: `gpt-4o` - 响应最快
- **备选**: `gemini-2.0-flash` - 速度和质量平衡

### 成本敏感场景
- **首选**: `grok-3-mini-beta` - 轻量级模型
- **备选**: `o4-mini` - 小型但高效

## 🔧 故障排除

### 常见问题

**1. API密钥错误**
```
❌ QINGYUN_API_KEY环境变量未设置
```
**解决方案**: 检查 `.env` 文件中的 `QINGYUN_API_KEY` 配置

**2. 网络连接问题**
```
❌ API连接失败: Connection timeout
```
**解决方案**: 检查网络连接，确认可以访问 `https://api.qingyuntop.top`

**3. 模型不可用**
```
❌ 模型 xxx 不可用: Model not found
```
**解决方案**: 检查模型名称是否正确，或该模型是否已下线

**4. 速率限制**
```
❌ Rate limit exceeded
```
**解决方案**: 等待一段时间后重试，或减少并发请求

### 调试技巧

1. **使用连接测试**:
   ```bash
   python test_api/test_qingyun_quick.py --connection
   ```

2. **测试单个模型**:
   ```bash
   python test_api/test_qingyun_quick.py --model "gpt-4o"
   ```

3. **查看详细错误**:
   综合测试脚本会保存详细的JSON结果文件，包含完整的错误信息

## 📈 性能优化建议

### 1. 模型选择优化
- 根据任务复杂度选择合适的模型
- 简单任务使用轻量级模型
- 复杂分析使用高性能模型

### 2. 请求优化
- 合理设置超时时间
- 避免过于频繁的请求
- 使用批处理减少API调用

### 3. 错误处理
- 实现重试机制
- 设置合理的fallback模型
- 监控API使用情况

## 🔄 定期测试建议

建议定期运行测试以确保模型可用性：

```bash
# 每日快速检查
python test_api/test_qingyun_quick.py

# 每周综合评估
python test_api/test_qingyun_comprehensive.py --quick

# 每月完整测试
python test_api/test_qingyun_comprehensive.py
```

## 📞 技术支持

如果遇到问题，请：

1. 首先运行连接测试确认基础配置
2. 查看测试脚本的详细输出和错误信息
3. 检查QingYun API的服务状态
4. 参考本文档的故障排除部分

---

*最后更新: 2025-06-27*
