#!/usr/bin/env python3
"""
QingYun API 快速测试脚本
快速测试qinyun API提供的模型基本功能
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider

class QingYunQuickTester:
    """QingYun模型快速测试器"""
    
    def __init__(self):
        """初始化测试器"""
        # 加载环境变量
        load_dotenv()
        
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("❌ QINGYUN_API_KEY环境变量未设置。请在.env文件中设置您的QingYun API密钥。")
        
        # 推荐测试的模型（按优先级排序）
        self.recommended_models = [
            "gpt-4o",                           # OpenAI GPT-4o
            "claude-3-5-sonnet-latest",         # Anthropic Claude
            "meta-llama/llama-4-scout",         # Meta Llama
            "gemini-2.0-flash",                 # Google Gemini
            "grok-3-mini-beta"                  # xAI Grok
        ]
        
        print(f"🚀 QingYun API 快速测试器")
        print(f"✅ API密钥已配置: {self.api_key[:10]}...")
        print(f"📋 将测试 {len(self.recommended_models)} 个推荐模型")
        print("-" * 50)

    def test_model(self, model_name: str) -> dict:
        """测试单个模型"""
        print(f"\n🧪 测试模型: {model_name}")
        
        try:
            start_time = time.time()
            
            # 获取模型
            llm = get_model(model_name, ModelProvider.QINGYUN)
            
            # 简单测试提示
            test_prompt = "请用一句话介绍人工智能。"
            
            # 调用模型
            response = llm.invoke(test_prompt)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 获取响应内容
            response_text = response.content if hasattr(response, 'content') else str(response)
            
            # 评估响应
            success = len(response_text.strip()) > 10
            
            result = {
                "model_name": model_name,
                "success": success,
                "response_time": response_time,
                "response": response_text[:100] + "..." if len(response_text) > 100 else response_text,
                "error": None
            }
            
            if success:
                print(f"  ✅ 成功 | 耗时: {response_time:.2f}s")
                print(f"  📝 响应: {result['response']}")
            else:
                print(f"  ❌ 响应过短或无效")
            
            return result
            
        except Exception as e:
            error_msg = str(e)
            print(f"  ❌ 失败: {error_msg}")
            
            return {
                "model_name": model_name,
                "success": False,
                "response_time": 0,
                "response": None,
                "error": error_msg
            }

    def run_quick_test(self):
        """运行快速测试"""
        print(f"\n🚀 开始快速测试...")
        results = []
        
        for i, model_name in enumerate(self.recommended_models, 1):
            print(f"\n[{i}/{len(self.recommended_models)}]", end=" ")
            
            try:
                result = self.test_model(model_name)
                results.append(result)
                
                # 避免API限制
                if i < len(self.recommended_models):
                    time.sleep(1)
                    
            except KeyboardInterrupt:
                print(f"\n⚠️ 用户中断测试")
                break
        
        return results

    def generate_summary(self, results: list) -> str:
        """生成测试摘要"""
        if not results:
            return "❌ 没有测试结果"
        
        successful = [r for r in results if r["success"]]
        failed = [r for r in results if not r["success"]]
        
        summary = []
        summary.append("=" * 50)
        summary.append("📊 QingYun API 快速测试摘要")
        summary.append("=" * 50)
        summary.append(f"总测试模型: {len(results)}")
        summary.append(f"成功模型: {len(successful)}")
        summary.append(f"失败模型: {len(failed)}")
        summary.append(f"成功率: {len(successful)/len(results)*100:.1f}%")
        
        if successful:
            summary.append(f"\n✅ 可用模型:")
            for result in successful:
                summary.append(f"   • {result['model_name']} (耗时: {result['response_time']:.2f}s)")
        
        if failed:
            summary.append(f"\n❌ 不可用模型:")
            for result in failed:
                summary.append(f"   • {result['model_name']}: {result['error']}")
        
        if successful:
            fastest = min(successful, key=lambda x: x["response_time"])
            summary.append(f"\n💡 推荐使用:")
            summary.append(f"   🥇 最快响应: {fastest['model_name']} ({fastest['response_time']:.2f}s)")
        
        summary.append(f"\n⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        summary.append("=" * 50)
        
        return "\n".join(summary)

def test_connection():
    """测试API连接"""
    print("🔗 测试QingYun API连接...")
    
    try:
        load_dotenv()
        api_key = os.getenv("QINGYUN_API_KEY")
        
        if not api_key:
            print("❌ QINGYUN_API_KEY未设置")
            return False
        
        # 测试最简单的模型
        llm = get_model("gpt-4o", ModelProvider.QINGYUN)
        response = llm.invoke("Hello")
        
        if response:
            print("✅ API连接正常")
            return True
        else:
            print("❌ API响应为空")
            return False
            
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="QingYun API 快速测试工具")
    parser.add_argument("--connection", "-c", action="store_true", help="仅测试API连接")
    parser.add_argument("--model", "-m", type=str, help="测试指定模型")
    
    args = parser.parse_args()
    
    try:
        if args.connection:
            # 仅测试连接
            success = test_connection()
            if success:
                print("\n📋 连接正常，可以开始使用QingYun模型")
                print("运行完整测试: python test_api/test_qingyun_quick.py")
            return
        
        if args.model:
            # 测试指定模型
            print(f"🧪 测试指定模型: {args.model}")
            tester = QingYunQuickTester()
            result = tester.test_model(args.model)
            
            if result["success"]:
                print(f"\n✅ 模型 {args.model} 可用!")
                print(f"📝 示例响应: {result['response']}")
            else:
                print(f"\n❌ 模型 {args.model} 不可用")
                print(f"错误: {result['error']}")
            return
        
        # 运行快速测试
        tester = QingYunQuickTester()
        results = tester.run_quick_test()
        
        # 显示摘要
        summary = tester.generate_summary(results)
        print(f"\n{summary}")
        
        # 使用建议
        successful = [r for r in results if r["success"]]
        if successful:
            print(f"\n📋 使用方法:")
            print(f"1. 在回测中选择: [qingyun] 开头的模型")
            print(f"2. 程序化使用:")
            print(f"   from src.llm.models import get_model, ModelProvider")
            print(f"   llm = get_model('{successful[0]['model_name']}', ModelProvider.QINGYUN)")
        else:
            print(f"\n🔧 故障排除:")
            print(f"1. 检查QINGYUN_API_KEY是否正确")
            print(f"2. 检查网络连接")
            print(f"3. 确认QingYun API服务状态")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
